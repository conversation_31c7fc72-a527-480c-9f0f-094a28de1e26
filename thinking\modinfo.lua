name = "商旅巡游录 | Caravan Cycle"
description = [[
A+B 综合纯代码玩法包：
- 每日词条（Daily Mutators）
- 远征合约（Contracts）与 Favor 恩惠
- 被动词条（Boons）
- 阵营声望（Factions）
- 商队巡游（Caravans）
- Boss 行为进化（Boss Evolutions）
无新增贴图/音效，兼容性友好，默认中等强度。]]

author = "Thinking Team"
version = "0.1.0"

forumthread = ""

api_version = 10
priority = 0

icon_atlas = nil
icon = nil

-- DST flags
client_only_mod = false
dst_compatible = true
all_clients_require_mod = true
server_filter_tags = {"caravan", "mutators", "contracts", "factions"}

-- Compatibility
reign_of_giants_compatible = true
forge_compatible = false
gorge_compatible = false

dont_starve_compatible = false -- DST only

configuration_options = {
    { name = "difficulty", label = "难度 Difficulty", options = {
        {description = "轻度", data = "easy"},
        {description = "中等", data = "normal"},
        {description = "偏硬核", data = "hard"},
    }, default = "normal" },

    { name = "mutator_count", label = "每日词条数量", options = {
        {description = "1", data = 1},
        {description = "2", data = 2},
        {description = "3", data = 3},
    }, default = 2 },

    { name = "contracts_concurrent", label = "并发合约数", options = {
        {description = "3", data = 3},
        {description = "4", data = 4},
        {description = "5", data = 5},
    }, default = 4 },

    { name = "favor_multiplier", label = "Favor 奖励倍率", options = {
        {description = "0.75x", data = 0.75},
        {description = "1.0x", data = 1.0},
        {description = "1.25x", data = 1.25},
    }, default = 1.0 },

    { name = "caravan_period", label = "商队周期（天）", options = {
        {description = "5", data = 5},
        {description = "7", data = 7},
        {description = "10", data = 10},
    }, default = 7 },

    { name = "enable_boss_evo", label = "启用 Boss 进化", options = {
        {description = "开启", data = true},
        {description = "关闭", data = false},
    }, default = true },
}

