-- 词条效果系统：实现各种词条的实际游戏效果

-- 确保能访问全局变量
local GLOBAL = GLOBAL

local MutatorEffects = {}

local function IsMasterSim()
    return GLOBAL.TheWorld ~= nil and GLOBAL.TheWorld.ismastersim
end

-- 获取当前激活的词条
local function GetActiveMutators()
    local world_comp = GLOBAL.TheWorld and GLOBAL.TheWorld.components and GLOBAL.TheWorld.components.modworld_global
    return world_comp and world_comp.mutators or {}
end

-- 检查是否有指定词条激活
local function HasMutator(mutator_id)
    local mutators = GetActiveMutators()
    for _, mutator in ipairs(mutators) do
        if mutator.id == mutator_id then
            return true
        end
    end
    return false
end

-- 正向词条效果实现
local PositiveEffects = {
    -- 丰收之日：采集额外获得1个相同物品
    bountiful_harvest = function(player)
        if not player or not player.components then return end

        -- 监听采集事件
        local function OnPickSomething(inst, data)
            if not IsMasterSim() then return end
            if not (data and data.object and data.loot) then return end
            local target = data.object

            -- 根据官方文档，data.loot可能是单个物品或物品列表
            local loot_items = {}
            if type(data.loot) == "table" and #data.loot > 0 then
                -- 如果是列表
                loot_items = data.loot
            elseif data.loot.prefab then
                -- 如果是单个物品实体
                loot_items = {data.loot}
            else
                return
            end

            -- 延迟一帧发放，避免和原逻辑同帧混堆
            inst:DoTaskInTime(0, function()
                if not inst:IsValid() or inst:HasTag("playerghost") then return end

                -- 为每个掉落物品生成额外的一个
                for _, loot_item in ipairs(loot_items) do
                    local prefab_name = loot_item.prefab or loot_item
                    if type(prefab_name) == "string" then
                        local extra = GLOBAL.SpawnPrefab(prefab_name)
                        if extra then
                            -- 如果原物品有堆叠，复制堆叠数量
                            if loot_item.components and loot_item.components.stackable and extra.components.stackable then
                                local stack_size = loot_item.components.stackable:StackSize()
                                extra.components.stackable:SetStackSize(stack_size)
                            end

                            if inst.components.inventory and inst.components.inventory:GiveItem(extra, nil, inst:GetPosition()) then
                                -- 给背包成功
                            else
                                -- 如果背包满了，掉落在地上
                                if target and target:IsValid() then
                                    local x, y, z = target.Transform:GetWorldPosition()
                                    extra.Transform:SetPosition(x, y, z)
                                else
                                    extra:Remove()
                                end
                            end
                            print("[商旅巡游录] 丰收之日：额外获得", prefab_name)
                        end
                    end
                end
            end)
        end

        player:ListenForEvent("picksomething", OnPickSomething)

        return function()
            if player and player:IsValid() then
                player:RemoveEventCallback("picksomething", OnPickSomething)
            end
        end
    end,
    
    -- 疾风步伐：移动速度大幅提升
    swift_feet = function(player)
        if not player or not player.components or not player.components.locomotor then return end
        
        player.components.locomotor:SetExternalSpeedMultiplier(player, "caravan_swift_feet", 1.5)
        print("[商旅巡游录] 疾风步伐：移速提升50%")

        return function()
            if player and player:IsValid() and player.components.locomotor then
                player.components.locomotor:RemoveExternalSpeedMultiplier(player, "caravan_swift_feet")
            end
        end
    end,
    
    -- 铁胃：饥饿值消耗减半
    iron_stomach = function(player)
        if not player or not player.components or not player.components.hunger then return end
        
        local original_rate = player.components.hunger.hungerrate or GLOBAL.TUNING.WILSON_HUNGER_RATE
        player.components.hunger:SetRate(original_rate * 0.5)
        print("[商旅巡游录] 铁胃：饥饿消耗减半")

        return function()
            if player and player:IsValid() and player.components.hunger then
                player.components.hunger:SetRate(original_rate)
            end
        end
    end,
    
    -- 澄澈心智：理智值不会自然下降
    clear_mind = function(player)
        if not player or not player.components or not player.components.sanity then return end

        -- 仅屏蔽夜晚/黑暗带来的自然理智下降（不影响怪物等外部光环）
        player.components.sanity:SetLightDrainImmune(true)
        print("[商旅巡游录] 澄澈心智：免疫黑暗理智流失")

        return function()
            if player and player:IsValid() and player.components.sanity then
                player.components.sanity:SetLightDrainImmune(false)
            end
        end
    end,
    
    -- 夜视：夜晚视野如同白昼
    night_vision = function(player)
        if not player or not player.components then return end

        -- 夜视效果：生成一个网络友好的随身光源预制体
        if player._caravan_night_vision_light and player._caravan_night_vision_light:IsValid() then
            player._caravan_night_vision_light:Remove()
            player._caravan_night_vision_light = nil
        end

        local light = GLOBAL.SpawnPrefab("caravan_light")
        if light then
            light.entity:SetParent(player.entity)
            light.Transform:SetPosition(0, 0, 0)
            player._caravan_night_vision_light = light
        end

        print("[商旅巡游录] 夜视：获得夜视能力")

        return function()
            if player and player:IsValid() and player._caravan_night_vision_light then
                player._caravan_night_vision_light:Remove()
                player._caravan_night_vision_light = nil
            end
        end
    end,

    -- 幸运打击：攻击有概率造成双倍伤害
    lucky_strike = function(player)
        if not player or not player.components or not player.components.combat then return end

        local original_calc_damage = player.components.combat.CalcDamage
        player.components.combat.CalcDamage = function(self, target, weapon, multiplier)
            local damage = original_calc_damage(self, target, weapon, multiplier)
            if math.random() < 0.25 then -- 25%概率
                damage = damage * 2
                if player.components.talker then
                    player.components.talker:Say("暴击！")
                end
                print("[商旅巡游录] 幸运打击：造成双倍伤害")
            end
            return damage
        end
        print("[商旅巡游录] 幸运打击：25%概率双倍伤害")

        return function()
            if player and player:IsValid() and player.components.combat then
                player.components.combat.CalcDamage = original_calc_damage
            end
        end
    end,

    -- 工匠大师：制作物品消耗材料减半
    master_crafter = function(player)
        if not player or not player.components or not player.components.builder then return end

        -- 通过修改builder的RemoveIngredients方法来实现材料减半
        local original_remove_ingredients = player.components.builder.RemoveIngredients
        player.components.builder.RemoveIngredients = function(self, ingredients, recname, discounted)
            -- 创建减半的材料表
            local reduced_ingredients = {}
            for _, ingredient in ipairs(ingredients) do
                local reduced_amount = math.max(1, math.ceil(ingredient.amount * 0.5))
                table.insert(reduced_ingredients, {type = ingredient.type, amount = reduced_amount})
            end
            return original_remove_ingredients(self, reduced_ingredients, recname, discounted)
        end

        print("[商旅巡游录] 工匠大师：制作材料消耗减半")

        return function()
            if player and player:IsValid() and player.components.builder then
                player.components.builder.RemoveIngredients = original_remove_ingredients
            end
        end
    end,

    -- 兽语者：中性生物不会主动攻击
    beast_friend = function(player)
        if not player or not player.components then return end

        -- 添加多个标签来影响不同类型的生物
        player:AddTag("beast_friend")
        player:AddTag("notarget") -- 通用的不被攻击标签
        player:AddTag("spiderwhisperer") -- 蜘蛛友好标签
        print("[商旅巡游录] 兽语者：中性生物不会主动攻击")

        return function()
            if player and player:IsValid() then
                player:RemoveTag("beast_friend")
                player:RemoveTag("notarget")
                player:RemoveTag("spiderwhisperer")
            end
        end
    end,
}

-- 负向词条效果实现
local NegativeEffects = {
    -- 脆弱工具：工具耐久消耗翻倍
    fragile_tools = function(player)
        if not player or not player.components then return end
        
        local function OnWorking(inst, _)
            local tool = inst.components.inventory and inst.components.inventory:GetEquippedItem(GLOBAL.EQUIPSLOTS.HANDS)
            if tool and tool.components and tool.components.finiteuses then
                -- 额外消耗1点耐久
                tool.components.finiteuses:Use(1)
                print("[商旅巡游录] 脆弱工具：额外消耗耐久")
            end
        end
        
        player:ListenForEvent("working", OnWorking)

        return function()
            if player and player:IsValid() then
                player:RemoveEventCallback("working", OnWorking)
            end
        end
    end,
    
    -- 不眠之夜：夜晚理智快速流失
    restless_night = function(player)
        if not player or not player.components or not player.components.sanity then return end
        
        local function CheckNightSanity()
            if GLOBAL.TheWorld and GLOBAL.TheWorld.state and GLOBAL.TheWorld.state.isnight then
                player.components.sanity:DoDelta(-2) -- 每秒-2理智
            end
        end
        
        local task = player:DoPeriodicTask(1, CheckNightSanity)
        print("[商旅巡游录] 不眠之夜：夜晚理智快速流失")
        
        return function()
            if task then
                task:Cancel()
                task = nil
            end
        end
    end,
    
    -- 笨拙之手：有概率掉落手持物品
    clumsy_hands = function(player)
        if not player or not player.components then return end
        
        local function OnWorking(inst, _)
            if math.random() < 0.1 then -- 10%概率
                local tool = inst.components.inventory and inst.components.inventory:GetEquippedItem(GLOBAL.EQUIPSLOTS.HANDS)
                if tool then
                    inst.components.inventory:DropItem(tool)
                    if inst.components.talker then
                        inst.components.talker:Say("手滑了！")
                    end
                    print("[商旅巡游录] 笨拙之手：掉落工具")
                end
            end
        end
        
        player:ListenForEvent("working", OnWorking)

        return function()
            if player and player:IsValid() then
                player:RemoveEventCallback("working", OnWorking)
            end
        end
    end,
    
    -- 负重前行：携带物品超过15个时移动缓慢
    heavy_burden = function(player)
        if not player or not player.components then return end
        
        local function CheckInventory()
            local inventory = player.components.inventory
            if inventory then
                local item_count = inventory:NumItems()

                if item_count > 15 then
                    if player.components.locomotor then
                        player.components.locomotor:SetExternalSpeedMultiplier(player, "caravan_heavy_burden", 0.5)
                    end
                else
                    if player.components.locomotor then
                        player.components.locomotor:RemoveExternalSpeedMultiplier(player, "caravan_heavy_burden")
                    end
                end
            end
        end
        
        local task = player:DoPeriodicTask(0.5, CheckInventory)
        print("[商旅巡游录] 负重前行：物品过多时移动缓慢")
        
        return function()
            if task then task:Cancel() end
            if player and player:IsValid() and player.components.locomotor then
                player.components.locomotor:RemoveExternalSpeedMultiplier(player, "caravan_heavy_burden")
            end
        end
    end,

    -- 怪物磁铁：敌对生物更容易发现你
    monster_magnet = function(player)
        if not player or not player.components then return end

        -- 增加玩家的威胁值，让怪物更容易攻击
        local original_threat = 0
        if player.components.combat then
            original_threat = player.components.combat.defaultdamage or 0
            player.components.combat:SetDefaultDamage(original_threat + 50) -- 增加威胁值
        end

        -- 添加标签让怪物优先攻击
        player:AddTag("monster_target")
        print("[商旅巡游录] 怪物磁铁：更容易被怪物发现")

        return function()
            if player and player:IsValid() then
                if player.components.combat then
                    player.components.combat:SetDefaultDamage(original_threat)
                end
                player:RemoveTag("monster_target")
            end
        end
    end,

    -- 阴雨连绵：持续下雨，潮湿值上升
    wet_weather = function(player)
        if not player or not player.components then return end

        local function AddMoisture()
            if player.components.moisture then
                player.components.moisture:DoDelta(2) -- 每秒+2潮湿
            end
        end

        local task = player:DoPeriodicTask(1, AddMoisture)
        print("[商旅巡游录] 阴雨连绵：持续增加潮湿值")

        return function()
            if task then
                task:Cancel()
                task = nil
            end
        end
    end,

    -- 寒流来袭：温度持续下降
    cold_snap = function(player)
        if not player or not player.components then return end

        local function ReduceTemperature()
            if player.components.temperature then
                player.components.temperature:DoDelta(-5) -- 每秒-5温度
            end
        end

        local task = player:DoPeriodicTask(1, ReduceTemperature)
        print("[商旅巡游录] 寒流来袭：持续降低温度")

        return function()
            if task then
                task:Cancel()
                task = nil
            end
        end
    end,

    -- 腐败加速：食物腐烂速度翻倍
    food_spoilage = function(player)
        if not player or not player.components or not player.components.inventory then return end

        local function AccelerateSpoilage()
            player.components.inventory:ForEachItem(function(item)
                if item.components.perishable then
                    item.components.perishable:ReducePercent(0.01) -- 每次减少1%新鲜度
                end
            end)
        end

        local task = player:DoPeriodicTask(1, AccelerateSpoilage)
        print("[商旅巡游录] 腐败加速：食物腐烂速度翻倍")

        return function()
            if task then
                task:Cancel()
                task = nil
            end
        end
    end,
}

-- 中性词条效果实现
local NeutralEffects = {
    -- 玻璃大炮：攻击力翻倍，但生命值减半
    glass_cannon = function(player)
        if not player or not player.components then return end

        local original_health = 0

        if player.components.health then
            original_health = player.components.health.maxhealth
            player.components.health:SetMaxHealth(original_health * 0.5)
            -- 同时设置当前生命值，避免超过新的最大值
            if player.components.health.currenthealth > player.components.health.maxhealth then
                player.components.health.currenthealth = player.components.health.maxhealth
            end
        end

        -- 使用外部伤害倍率系统，更安全
        if player.components.combat and player.components.combat.externaldamagemultipliers then
            player.components.combat.externaldamagemultipliers:SetModifier(player, 2.0, "caravan_glass_cannon")
        end

        print("[商旅巡游录] 玻璃大炮：攻击翻倍，生命减半")

        return function()
            if player and player:IsValid() then
                if player.components.health then
                    player.components.health:SetMaxHealth(original_health)
                end
                if player.components.combat and player.components.combat.externaldamagemultipliers then
                    player.components.combat.externaldamagemultipliers:RemoveModifier(player, "caravan_glass_cannon")
                end
            end
        end
    end,

    -- 夜猫子：夜晚获得各种加成，白天虚弱
    night_owl = function(player)
        if not player or not player.components then return end

        local function CheckTimeEffects()
            if GLOBAL.TheWorld and GLOBAL.TheWorld.state then
                if GLOBAL.TheWorld.state.isnight then
                    -- 夜晚加成：移速+30%，攻击力+25%
                    if player.components.locomotor then
                        player.components.locomotor:SetExternalSpeedMultiplier(player, "caravan_night_owl_speed", 1.3)
                    end
                    if player.components.combat and player.components.combat.externaldamagemultipliers then
                        player.components.combat.externaldamagemultipliers:SetModifier(player, 1.25, "caravan_night_owl_damage")
                    end
                elseif GLOBAL.TheWorld.state.isday then
                    -- 白天虚弱：移速-20%，攻击力-20%
                    if player.components.locomotor then
                        player.components.locomotor:SetExternalSpeedMultiplier(player, "caravan_night_owl_speed", 0.8)
                    end
                    if player.components.combat and player.components.combat.externaldamagemultipliers then
                        player.components.combat.externaldamagemultipliers:SetModifier(player, 0.8, "caravan_night_owl_damage")
                    end
                else
                    -- 黄昏：正常状态
                    if player.components.locomotor then
                        player.components.locomotor:RemoveExternalSpeedMultiplier(player, "caravan_night_owl_speed")
                    end
                    if player.components.combat and player.components.combat.externaldamagemultipliers then
                        player.components.combat.externaldamagemultipliers:RemoveModifier(player, "caravan_night_owl_damage")
                    end
                end
            end
        end

        local task = player:DoPeriodicTask(5, CheckTimeEffects) -- 每5秒检查一次
        CheckTimeEffects() -- 立即检查一次
        print("[商旅巡游录] 夜猫子：夜晚强化，白天虚弱")

        return function()
            if task then
                task:Cancel()
                task = nil
            end
            if player and player:IsValid() then
                if player.components.locomotor then
                    player.components.locomotor:RemoveExternalSpeedMultiplier(player, "caravan_night_owl_speed")
                end
                if player.components.combat and player.components.combat.externaldamagemultipliers then
                    player.components.combat.externaldamagemultipliers:RemoveModifier(player, "caravan_night_owl_damage")
                end
            end
        end
    end,

    -- 狂战士：生命值越低攻击力越高
    berserker = function(player)
        if not player or not player.components or not player.components.health or not player.components.combat then return end

        local original_calc_damage = player.components.combat.CalcDamage
        player.components.combat.CalcDamage = function(self, target, weapon, multiplier)
            local damage = original_calc_damage(self, target, weapon, multiplier)
            local health_percent = player.components.health.currenthealth / player.components.health.maxhealth
            local damage_bonus = 1 + (1 - health_percent) * 1.5 -- 生命越低伤害越高，最高+150%
            damage = damage * damage_bonus
            return damage
        end
        print("[商旅巡游录] 狂战士：生命越低攻击力越高")

        return function()
            if player and player:IsValid() and player.components.combat then
                player.components.combat.CalcDamage = original_calc_damage
            end
        end
    end,

    -- 商人造访：今日将有特殊商队到访
    merchant_visit = function(player)
        if not player or not player.components then return end

        -- 这是一个事件类词条，主要是提示作用
        print("[商旅巡游录] 商人造访：今日将有特殊商队到访")

        -- 可以在这里添加生成商队的逻辑
        -- 暂时只是一个占位符实现
        return function()
            -- 清理逻辑（如果有的话）
        end
    end,
}

-- 应用词条效果到玩家
function MutatorEffects.ApplyToPlayer(player)
    if not player or not player.components then
        print("[商旅巡游录] 错误：无效的玩家对象")
        return
    end

    -- 增强玩家有效性检查
    if not player.userid or not player:IsValid() or player:HasTag("playerghost") then
        print("[商旅巡游录] 跳过无效或幽灵状态玩家")
        return
    end

    -- 检查玩家是否在主服务器上
    if not IsMasterSim() then
        print("[商旅巡游录] 非主服务器，跳过词条效果应用")
        return
    end

    -- 清除之前的效果
    MutatorEffects.RemoveFromPlayer(player)

    -- 存储清理函数
    player._caravan_mutator_cleanups = {}

    local mutators = GetActiveMutators()
    if #mutators == 0 then
        print("[商旅巡游录] 没有激活的词条")
        return
    end

    local applied_effects = 0
    for _, mutator in ipairs(mutators) do
        local effect_func = nil

        if mutator.type == "positive" and PositiveEffects[mutator.id] then
            effect_func = PositiveEffects[mutator.id]
        elseif mutator.type == "negative" and NegativeEffects[mutator.id] then
            effect_func = NegativeEffects[mutator.id]
        elseif mutator.type == "neutral" and NeutralEffects[mutator.id] then
            effect_func = NeutralEffects[mutator.id]
        elseif mutator.type == "event" and NeutralEffects[mutator.id] then
            effect_func = NeutralEffects[mutator.id]
        end

        if effect_func then
            local success, result = pcall(effect_func, player)
            if success then
                applied_effects = applied_effects + 1
                if result and type(result) == "function" then
                    table.insert(player._caravan_mutator_cleanups, result)
                end
                print("[商旅巡游录] 成功应用词条效果:", mutator.id, "到玩家", player.userid)
            else
                print("[商旅巡游录] 应用词条效果失败:", mutator.id, "错误:", result)
            end
        else
            print("[商旅巡游录] 未找到词条效果实现:", mutator.id, mutator.type)
        end
    end

    print(string.format("[商旅巡游录] 玩家 %s 应用了 %d/%d 个词条效果",
          player.userid or "unknown", applied_effects, #mutators))
end

-- 移除玩家的词条效果
function MutatorEffects.RemoveFromPlayer(player)
    if not player or not player._caravan_mutator_cleanups then return end
    
    for _, cleanup in ipairs(player._caravan_mutator_cleanups) do
        if type(cleanup) == "function" then
            cleanup()
        end
    end
    
    player._caravan_mutator_cleanups = {}
    print("[商旅巡游录] 已清除玩家词条效果", player.userid or "unknown")
end

return MutatorEffects
