local fuZ3z86 = require "widgets/screen"
local er = require("widgets/mapwidget")
local DFb100j = require "widgets/widget"
local XL_ = require "widgets/imagebutton"
local WYdR = require "widgets/redux/mythbookwidget"
local QKKks_zt = require "widgets/redux/templates"
local Are7xU = Class(fuZ3z86,
    function(yxjl, ZG)
        yxjl.owner = ZG; fuZ3z86._ctor(yxjl, "Mythbookpopupscreen")
        local Vu0cCAf = yxjl:AddChild(XL_("images/global.xml", "square.tex"))
        Vu0cCAf.image:SetVRegPoint(ANCHOR_MIDDLE)
        Vu0cCAf.image:SetHRegPoint(ANCHOR_MIDDLE)
        Vu0cCAf.image:SetVAnchor(ANCHOR_MIDDLE)
        Vu0cCAf.image:SetHAnchor(ANCHOR_MIDDLE)
        Vu0cCAf.image:SetScaleMode(SCALEMODE_FILLSCREEN)
        Vu0cCAf.image:SetTint(0, 0, 0, .5)
        Vu0cCAf:SetOnClick(function() TheFrontEnd:PopScreen() end)
        Vu0cCAf:SetHelpTextMessage("")
        local q = yxjl:AddChild(DFb100j("root"))
        q:SetScaleMode(SCALEMODE_PROPORTIONAL)
        q:SetHAnchor(ANCHOR_MIDDLE)
        q:SetVAnchor(ANCHOR_MIDDLE)
        q:SetPosition(0, -25)
        yxjl.book = q:AddChild(WYdR(ZG))
        yxjl.default_focus = yxjl.book
    end)
function Are7xU:OnDestroy()
    POPUPS.MYTHBOOKINFO:Close(self.owner)
    Are7xU._base.OnDestroy(self)
end; function Are7xU:OnBecomeInactive() Are7xU._base.OnBecomeInactive(self) end; function Are7xU:OnBecomeActive() Are7xU
        ._base.OnBecomeActive(self) end; function Are7xU:OnControl(kP7O5, lqT)
    if Are7xU._base.OnControl(self, kP7O5, lqT) then return true end; if not lqT and (kP7O5 == CONTROL_MAP or kP7O5 == CONTROL_CANCEL) then
        TheFrontEnd:GetSound():PlaySound("dontstarve/HUD/click_move")
        TheFrontEnd:PopScreen()
        return true
    end; return false
end; function Are7xU:GetHelpText()
    local mP3mlD = TheInput:GetControllerID()
    local PrPyxMK = {}
    table.insert(PrPyxMK, TheInput:GetLocalizedControl(mP3mlD, CONTROL_CANCEL) .. " " .. STRINGS.UI.HELP.BACK)
    return table.concat(PrPyxMK, "  ")
end; return Are7xU
