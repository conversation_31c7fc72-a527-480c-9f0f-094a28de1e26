local DFb100j = require "widgets/text"
local XL_ = require "widgets/image"
local WYdR = require "widgets/widget"
local QKKks_zt = require "widgets/uianim"
local Are7xU = require "widgets/mythbuffslot"
local yxjl = 0; local ZG = 90; local Vu0cCAf = 0.2; local q = Class(WYdR,
    function(kP7O5, lqT)
        WYdR._ctor(kP7O5, "MythBuffBar")
        kP7O5.owner = lqT; kP7O5.buffslots = {}
        kP7O5.anim_queue = {}
        kP7O5.time = 0; kP7O5.AnimTask = nil; kP7O5:SetHAnchor(1)
        kP7O5:SetVAnchor(1)
    end)
function q:EnableAnimTask(mP3mlD)
    if self.AnimTask then
        self.AnimTask:Cancel()
        self.AnimTask = nil
    end; if mP3mlD then self.AnimTask = self.inst:DoPeriodicTask(Vu0cCAf, function() self:PopAnim() end) end
end; function q:PushAnim(PrPyxMK)
    table.insert(self.anim_queue, PrPyxMK)
    if not self.AnimTask then self:EnableAnimTask(true) end
end; function q:PopAnim() if #self.anim_queue > 0 then
        self.anim_queue[1]()
        table.remove(self.anim_queue, 1)
    else self:EnableAnimTask(false) end end; function q:HasBuff(tczrIB) return self.buffslots[tczrIB] ~= nil end; function q:AddBuff(
    a, wqU76o, LB1Z, N9L, hDc_M) if not self.buffslots[a] then
        local qW0lRiD1 = self:AddChild(Are7xU(wqU76o, LB1Z, N9L, hDc_M))
        qW0lRiD1.add_time = GetTime()
        self.buffslots[a] = qW0lRiD1; if hDc_M then hDc_M:ListenForEvent("onremove", function() self:RemoveBuff(a) end) end; self
            :PushAnim(function()
                qW0lRiD1:SlideIn()
                self:Line()
            end)
    else end end; function q:UpdateBuff(iD1IUx, JLCOx_ak, hPQ, R1FIoQI) if self.buffslots[iD1IUx] then
        if JLCOx_ak then self.buffslots[iD1IUx]:SetImage(JLCOx_ak) end; if hPQ then self.buffslots[iD1IUx]:SetDescName(
            hPQ) end; if R1FIoQI then self.buffslots[iD1IUx]:SetTimeRemain(R1FIoQI) end
    else end end; function q:RemoveBuff(NsoTwDs, HGli)
    if HGli == nil then HGli = 0.33 end; local iy = self.buffslots[NsoTwDs]
    self.buffslots[NsoTwDs] = nil; self:PushAnim(function()
        iy:SlideOut(HGli)
        self:Line()
    end)
end; function q:ListByTime()
    local m6SCS0 = {}
    for NUhYw6R4, Hv in pairs(self.buffslots) do table.insert(m6SCS0, Hv) end; table.sort(m6SCS0,
        function(Ch, urkh) return Ch.add_time > urkh.add_time end)
    return m6SCS0
end; function q:Line(zhzpBSx) for rHSjalVy, TjhsnP in pairs(self:ListByTime()) do
        local t5jzEd9, JZAU2, zPXTTg = yxjl + ZG * (rHSjalVy - 1), 0, 0; TjhsnP:CancelMoveTo()
        if zhzpBSx then TjhsnP:SetPosition(t5jzEd9, JZAU2, zPXTTg) else
            local seMLr = TjhsnP:GetPosition()
            TjhsnP:MoveTo(seMLr, Vector3(t5jzEd9, JZAU2, zPXTTg), 0.5)
        end
    end end; return q
