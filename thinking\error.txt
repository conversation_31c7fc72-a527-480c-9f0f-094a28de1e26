
[00:01:17]: [string "../mods/thinking/scripts/components/modworl..."]:135: variable 'GLOBAL' is not declared
LUA ERROR stack traceback:
=[C]:-1 in (global) error (C) <-1--1>
scripts/strict.lua:23 in () ? (Lua) <21-26>
   t = table: 000000003AE31670
   n = GLOBAL
../mods/thinking/scripts/components/modworld_global.lua:135 in (method) ApplyMutatorEffectsToAllPlayers (Lua) <123-155>
   self =
      contracts = table: 0000000010F911F0
      inst = 100037 - world (valid:true)
      mutators = table: 0000000010F8FC60
      current_day = 3
      boss_state = table: 0000000010F90840
      _inited = true
      caravan = table: 0000000010F91470
   MutatorEffects = table: 00000000EFA7C2B0
   applied_count = 0
   total_players = 0
../mods/thinking/scripts/components/modworld_global.lua:39 in (field) fn (Lua) <38-40>
scripts/scheduler.lua:186 in (method) OnTick (Lua) <164-216>
   self =
      running = table: 00000000153FD770
      waitingfortick = table: 00000000153FD6D0
      hibernating = table: 00000000153FCDC0
      tasks = table: 00000000153FD5E0
      waking = table: 00000000EFA7CA30
      attime = table: 00000000153FCEB0
   tick = 31
   k = PERIODIC 100037: 1.000000
   v = true
   already_dead = false
scripts/scheduler.lua:409 in (global) RunScheduler (Lua) <407-415>
   tick = 31
scripts/update.lua:240 in () ? (Lua) <224-298>
   dt = 0.033333335071802
   tick = 31
   i = 31

[00:01:17]: [string "../mods/thinking/scripts/components/modworl..."]:135: variable 'GLOBAL' is not declared
LUA ERROR stack traceback:
    =[C]:-1 in (global) error (C) <-1--1>
    scripts/strict.lua:23 in () ? (Lua) <21-26>
    ../mods/thinking/scripts/components/modworld_global.lua:135 in (method) ApplyMutatorEffectsToAllPlayers (Lua) <123-155>
    ../mods/thinking/scripts/components/modworld_global.lua:39 in (field) fn (Lua) <38-40>
    scripts/scheduler.lua:186 in (method) OnTick (Lua) <164-216>
    scripts/scheduler.lua:409 in (global) RunScheduler (Lua) <407-415>
    scripts/update.lua:240 in () ? (Lua) <224-298>
	
[00:01:23]: Collecting garbage...
[00:01:23]: lua_gc took 0.30 seconds
[00:01:23]: ~ShardLuaProxy()
[00:01:23]: ~cEventLeaderboardProxy()
[00:01:23]: ~ItemServerLuaProxy()
[00:01:23]: ~InventoryLuaProxy()
[00:01:23]: ~NetworkLuaProxy()
[00:01:23]: ~SimLuaProxy()
[00:01:23]: [Workshop] CancelDownloads for all pending downloads
[00:01:23]: lua_close took 0.44 seconds
[00:01:23]: ReleaseAll
[00:01:23]: ReleaseAll Finished
[00:01:23]: cGame::StartPlaying
[00:01:23]: AppVersion::GetArchitecture() x64
[00:01:23]: LOADING LUA
[00:01:23]: DoLuaFile scripts/main.lua
[00:01:23]: DoLuaFile loading buffer scripts/main.lua
