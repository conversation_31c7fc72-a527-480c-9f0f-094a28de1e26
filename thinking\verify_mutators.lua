-- 验证脚本：检查所有词条是否都有对应的效果实现

-- 词条池中的所有词条ID
local MUTATOR_IDS = {
    -- 正向词条
    "bountiful_harvest", "swift_feet", "iron_stomach", "clear_mind", 
    "lucky_strike", "master_crafter", "night_vision", "beast_friend",
    
    -- 负向词条
    "fragile_tools", "restless_night", "clumsy_hands", "heavy_burden",
    "monster_magnet", "wet_weather", "cold_snap", "food_spoilage",
    
    -- 中性/事件词条
    "glass_cannon", "night_owl", "berserker", "merchant_visit"
}

-- 在mutator_effects.lua中实现的效果ID
local IMPLEMENTED_EFFECTS = {
    -- PositiveEffects
    "bountiful_harvest", "swift_feet", "iron_stomach", "clear_mind",
    "lucky_strike", "master_crafter", "night_vision", "beast_friend",
    
    -- NegativeEffects  
    "fragile_tools", "restless_night", "clumsy_hands", "heavy_burden",
    "monster_magnet", "wet_weather", "cold_snap", "food_spoilage",
    
    -- NeutralEffects
    "glass_cannon", "night_owl", "berserker", "merchant_visit"
}

-- 验证函数
local function VerifyMutators()
    print("=== 词条系统验证 ===")
    
    local missing_implementations = {}
    local extra_implementations = {}
    
    -- 检查是否有词条缺少实现
    for _, mutator_id in ipairs(MUTATOR_IDS) do
        local found = false
        for _, impl_id in ipairs(IMPLEMENTED_EFFECTS) do
            if mutator_id == impl_id then
                found = true
                break
            end
        end
        if not found then
            table.insert(missing_implementations, mutator_id)
        end
    end
    
    -- 检查是否有多余的实现
    for _, impl_id in ipairs(IMPLEMENTED_EFFECTS) do
        local found = false
        for _, mutator_id in ipairs(MUTATOR_IDS) do
            if impl_id == mutator_id then
                found = true
                break
            end
        end
        if not found then
            table.insert(extra_implementations, impl_id)
        end
    end
    
    -- 输出结果
    if #missing_implementations == 0 and #extra_implementations == 0 then
        print("✓ 所有词条都有对应的效果实现")
        print("✓ 没有多余的效果实现")
        print("✓ 词条系统验证通过")
    else
        if #missing_implementations > 0 then
            print("✗ 缺少效果实现的词条:")
            for _, id in ipairs(missing_implementations) do
                print("  - " .. id)
            end
        end
        
        if #extra_implementations > 0 then
            print("✗ 多余的效果实现:")
            for _, id in ipairs(extra_implementations) do
                print("  - " .. id)
            end
        end
    end
    
    print("总词条数:", #MUTATOR_IDS)
    print("总实现数:", #IMPLEMENTED_EFFECTS)
    print("===================")
end

-- 运行验证
VerifyMutators()

return {
    VerifyMutators = VerifyMutators,
    MUTATOR_IDS = MUTATOR_IDS,
    IMPLEMENTED_EFFECTS = IMPLEMENTED_EFFECTS
}
