-- Alternative approach: Hook into chat system directly
local STRINGS = GLOBAL.STRINGS
local TheNet = GLOBAL.TheNet

local function IsMaster() return TheNet:GetIsServer() end

local function Broadcast(msg)
    if TheNet:GetIsServer() then
        TheNet:Announce("[商旅巡游录] " .. tostring(msg))
    end
end

-- Command registry
local commands = {}

local function RegisterCommand(name, desc, fn)
    commands[name] = {
        desc = desc,
        fn = fn
    }
    print("[商旅巡游录] Registered command:", name)
end

-- Simple trim function
local function trim(s)
    return s:match("^%s*(.-)%s*$")
end

-- Hook into player chat to catch commands (without slash)
local function OnPlayerSay(player, message)
    if not player or not message then return end

    -- Check if message is exactly a command (no slash needed)
    local cmd = string.lower(trim(message))
    print("[商旅巡游录] Chat message detected:", cmd, "from", player.userid or "unknown")

    -- 不处理单个字母'v'，避免与正常聊天冲突
    -- 只处理明确的命令
    if commands[cmd] then
        print("[商旅巡游录] Executing command:", cmd)
        commands[cmd].fn(player)
        return true -- Consume the message
    end
    return false
end

-- Hook into the chat system
local function HookChatSystem()
    -- Hook into player talker component
    AddPlayerPostInit(function(player)
        if not player.components.talker then return end

        local old_say = player.components.talker.Say
        player.components.talker.OldSay = old_say  -- Save reference for commands
        player.components.talker.Say = function(self, message, ...)
            if OnPlayerSay(player, message) then
                -- Command was handled, don't broadcast the chat
                return
            end
            -- Normal chat, proceed
            return old_say(self, message, ...)
        end
    end)
end

-- Initialize the chat hook system
HookChatSystem()

-- Register commands with mod prefix to avoid conflicts
RegisterCommand("chelp", "显示商旅巡游录帮助", function(player)
    print("[商旅巡游录] Help command executed for player:", player and player.userid or "nil")
    if player and player.components and player.components.talker then
        -- Use the original Say function to avoid recursion
        local talker = player.components.talker
        if talker.OldSay then
            talker:OldSay(STRINGS.CARAVAN.HELP)
        else
            Broadcast(STRINGS.CARAVAN.HELP)
        end
    else
        Broadcast(STRINGS.CARAVAN.HELP)
    end
end)

RegisterCommand("cmutators", "查看今日词条", function(player)
    local w = GLOBAL.TheWorld
    local comp = w and w.components and w.components.modworld_global
    if comp then comp:CmdShowMutators(player) end
end)

RegisterCommand("creroll", "发起重掷词条投票", function(player)
    local w = GLOBAL.TheWorld
    local comp = w and w.components and w.components.modworld_global
    if comp then comp:CmdRerollMutators(player) end
end)

RegisterCommand("ccontracts", "查看当前合约", function(player)
    local w = GLOBAL.TheWorld
    local comp = w and w.components and w.components.modworld_global
    if comp then comp:CmdShowContracts(player) end
end)

RegisterCommand("cboons", "查看被动", function(player)
    if player and player.components and player.components.modplayer_boons then
        player.components.modplayer_boons:CmdShowBoons()
    end
end)

RegisterCommand("crep", "查看阵营声望", function(player)
    if player and player.components and player.components.modplayer_rep then
        player.components.modplayer_rep:CmdShowRep()
    end
end)

RegisterCommand("ccaravan", "查看商队信息", function(player)
    local w = GLOBAL.TheWorld
    local comp = w and w.components and w.components.modworld_global
    if comp then comp:CmdShowCaravan(player) end
end)

RegisterCommand("cui", "打开商旅巡游录UI界面", function(player)
    if player then
        -- Use the same toggle function as the V key
        if GLOBAL.ToggleCaravanUI then
            GLOBAL.ToggleCaravanUI()
        else
            -- Fallback method
            local CaravanScreen = GLOBAL.require("screens/caravanscreen")
            local screen = CaravanScreen(player)
            GLOBAL.TheFrontEnd:PushScreen(screen)
        end

        if player.components and player.components.talker then
            player.components.talker:Say("商旅巡游录界面")
        end
    end
end)

-- /caravan
RegisterCommand("caravan", "查看商队信息", function(player)
    local w = GLOBAL.TheWorld
    local comp = w and w.components and w.components.modworld_global
    if comp then comp:CmdShowCaravan(player) end
end)

-- 调试命令：重新应用所有词条效果
RegisterCommand("creapply", "重新应用词条效果到所有玩家", function(player)
    local world_comp = GLOBAL.TheWorld and GLOBAL.TheWorld.components and GLOBAL.TheWorld.components.modworld_global
    if world_comp then
        world_comp:ApplyMutatorEffectsToAllPlayers()
        if player and player.components and player.components.talker then
            player.components.talker:Say("已重新应用词条效果到所有玩家")
        end
    else
        if player and player.components and player.components.talker then
            player.components.talker:Say("世界组件未初始化")
        end
    end
end)

-- 调试命令：检查词条系统状态
RegisterCommand("cstatus", "检查词条系统状态", function(player)
    local world_comp = GLOBAL.TheWorld and GLOBAL.TheWorld.components and GLOBAL.TheWorld.components.modworld_global
    if world_comp then
        local current_day = (GLOBAL.TheWorld and GLOBAL.TheWorld.state and GLOBAL.TheWorld.state.cycles) or 0
        local mutator_count = #(world_comp.mutators or {})
        local player_count = #(GLOBAL.AllPlayers or {})

        local status = string.format("词条系统状态: 当前第%d天, %d个词条, %d个玩家在线",
                                   current_day, mutator_count, player_count)

        if player and player.components and player.components.talker then
            player.components.talker:Say(status)

            -- 显示当前词条
            if mutator_count > 0 then
                for i, mutator in ipairs(world_comp.mutators) do
                    local type_icon = ""
                    if mutator.type == "positive" then type_icon = "✓"
                    elseif mutator.type == "negative" then type_icon = "✗"
                    elseif mutator.type == "neutral" then type_icon = "◈"
                    elseif mutator.type == "event" then type_icon = "★"
                    end
                    player.components.talker:Say(string.format("[%d]%s%s", i, type_icon, mutator.desc))
                end
            end

            -- 显示玩家重投状态
            local reroll_comp = player.components.modplayer_reroll
            if reroll_comp then
                local can_reroll = reroll_comp:CanReroll() and "可以" or "不能"
                player.components.talker:Say(string.format("重投状态: %s重投", can_reroll))
            end
        end
    else
        if player and player.components and player.components.talker then
            player.components.talker:Say("世界组件未初始化")
        end
    end
end)