local Screen = require "widgets/screen"
local Widget = require "widgets/widget"
local Text = require "widgets/text"
local Image = require "widgets/image"
local ImageButton = require "widgets/imagebutton"
local TEMPLATES = require "widgets/redux/templates"

local CaravanScreen = Class(Screen, function(self, owner)
    Screen._ctor(self, "CaravanScreen")
    self.owner = owner

    -- 半透明背景
    self.bg = self:AddChild(ImageButton("images/global.xml", "square.tex"))
    self.bg.image:SetVRegPoint(ANCHOR_MIDDLE)
    self.bg.image:SetHRegPoint(ANCHOR_MIDDLE)
    self.bg.image:SetVAnchor(ANCHOR_MIDDLE)
    self.bg.image:SetHAnchor(ANCHOR_MIDDLE)
    self.bg.image:SetScaleMode(SCALEMODE_FILLSCREEN)
    self.bg.image:SetTint(0, 0, 0, 0.5)
    self.bg:SetOnClick(function() self:Close() end)
    self.bg:SetHelpTextMessage("")

    -- 主面板容器 - 正确设置居中
    self.root = self:AddChild(Widget("root"))
    self.root:SetScaleMode(SCALEMODE_PROPORTIONAL)
    self.root:SetHAnchor(ANCHOR_MIDDLE)
    self.root:SetVAnchor(ANCHOR_MIDDLE)
    self.root:SetPosition(0, 0)

    -- 主面板（移除底部按钮，留出更多空间）
    self.panel = self.root:AddChild(TEMPLATES.RectangleWindow(600, 500, "商旅巡游录", nil))

    -- 内容区域
    self.content = self.panel:AddChild(Widget("content"))
    self.content:SetPosition(0, 0) -- 居中，整体在窗口内部布局

    -- 标签页按钮
    self.tabs = {}
    self.current_tab = "mutators"
    
    local tab_names = {
        {id = "mutators", text = "今日词条"},
        {id = "contracts", text = "合约任务"},
        {id = "boons", text = "被动恩惠"},
        {id = "reputation", text = "阵营声望"},
        {id = "caravan", text = "商队信息"}
    }
    
    -- 标签页移至底部
    for i, tab in ipairs(tab_names) do
        local btn = self.content:AddChild(TEMPLATES.StandardButton(
            function() self:SwitchTab(tab.id) end,
            tab.text,
            {120, 40}
        ))
        btn:SetPosition(-200 + (i-1) * 100, -200) -- 底部靠下
        self.tabs[tab.id] = btn
    end

    -- 内容显示区域
    self.info_area = self.content:AddChild(Widget("info_area"))
    self.info_area:SetPosition(0, 0)

    -- 文本显示（恢复到原始位置）
    self.info_text = self.info_area:AddChild(Text(CHATFONT, 24, "", {1, 1, 1, 1}))
    self.info_text:SetPosition(0, 0)
    self.info_text:SetRegionSize(500, 300)
    self.info_text:SetHAlign(ANCHOR_LEFT)
    self.info_text:SetVAlign(ANCHOR_TOP)

    -- 操作按钮区域（紧贴底部标签之上）
    self.action_area = self.info_area:AddChild(Widget("action_area"))
    self.action_area:SetPosition(0, -150)

    -- 创建操作按钮（初始隐藏）
    self.action_buttons = {}

    self:RefreshData()
    self:SwitchTab("mutators")

    self.default_focus = self.root
end)

function CaravanScreen:SwitchTab(tab_id)
    self.current_tab = tab_id

    -- 更新按钮状态
    for id, btn in pairs(self.tabs) do
        if id == tab_id then
            btn:SetTextColour(1, 1, 0, 1) -- 黄色表示选中
        else
            btn:SetTextColour(1, 1, 1, 1) -- 白色表示未选中
        end
    end

    -- 清除旧的操作按钮
    for _, btn in pairs(self.action_buttons) do
        btn:Kill()
    end
    self.action_buttons = {}

    -- 根据标签页添加相应的操作按钮
    self:CreateActionButtons(tab_id)

    self:UpdateContent()
end

function CaravanScreen:CreateActionButtons(tab_id)
    if tab_id == "mutators" then
        -- 重掷词条按钮
        local player_reroll = self.owner and self.owner.components and self.owner.components.modplayer_reroll
        local can_reroll = player_reroll and player_reroll:CanReroll()

        local reroll_btn = self.action_area:AddChild(TEMPLATES.StandardButton(
            function() self:DoReroll() end,
            can_reroll and "重掷词条" or "已重掷",
            {120, 40}
        ))
        reroll_btn:SetPosition(0, 0) -- 居中显示

        -- 如果不能重掷，禁用按钮
        if not can_reroll then
            reroll_btn:SetTextColour(0.5, 0.5, 0.5, 1) -- 灰色
        end

        table.insert(self.action_buttons, reroll_btn)

    elseif tab_id == "boons" then
        -- 洗点按钮（未来功能）
        local respec_btn = self.action_area:AddChild(TEMPLATES.StandardButton(
            function() self:DoRespec() end,
            "洗点重置",
            {120, 40}
        ))
        respec_btn:SetPosition(0, 0)
        table.insert(self.action_buttons, respec_btn)
    end
    -- 其他标签页暂时不需要操作按钮
end

function CaravanScreen:DoReroll()
    local world_comp = TheWorld and TheWorld.components and TheWorld.components.modworld_global
    if world_comp then
        world_comp:CmdRerollMutators(self.owner)
        self:RefreshData()
    end
end

function CaravanScreen:DoRespec()
    -- 未来实现洗点功能
    if self.owner and self.owner.components and self.owner.components.talker then
        self.owner.components.talker:Say("洗点功能开发中...")
    end
end

function CaravanScreen:UpdateContent()
    local content = ""
    local world_comp = TheWorld and TheWorld.components and TheWorld.components.modworld_global
    local player_boons = self.owner and self.owner.components and self.owner.components.modplayer_boons
    local player_rep = self.owner and self.owner.components and self.owner.components.modplayer_rep

    if self.current_tab == "mutators" then
        content = "=== 今日词条 ===\n\n"
        if world_comp and world_comp.mutators then
            for i, mutator in ipairs(world_comp.mutators) do
                local type_icon = ""
                if mutator.type == "positive" then
                    type_icon = "✓ "
                elseif mutator.type == "negative" then
                    type_icon = "✗ "
                elseif mutator.type == "neutral" then
                    type_icon = "◈ "
                elseif mutator.type == "event" then
                    type_icon = "★ "
                end
                content = content .. string.format("[%d] %s%s\n", i, type_icon, mutator.desc or "未知词条")
            end
            content = content .. "\n图例：✓有利 ✗挑战 ◈平衡 ★事件\n"

            -- 检查玩家的重投状态
            local player_reroll = self.owner and self.owner.components and self.owner.components.modplayer_reroll
            if player_reroll then
                if player_reroll:CanReroll() then
                    content = content .. "\n你今日可以重掷词条（每人每日一次）"
                else
                    content = content .. "\n你今日已重掷过词条"
                end
            else
                content = content .. "\n可使用 creroll 重掷词条（每人每日一次）"
            end
        else
            content = content .. "暂无词条信息"
        end
        
    elseif self.current_tab == "contracts" then
        content = "=== 合约任务 ===\n\n"
        content = content .. "开发中...\n"
        content = content .. "将包含击杀、交付、建设等任务类型\n"
        content = content .. "完成任务可获得 Favor 代币和阵营声望"
        
    elseif self.current_tab == "boons" then
        content = "=== 被动恩惠 ===\n\n"
        if player_boons then
            content = content .. string.format("Favor 代币: %d\n", player_boons.favor or 0)
            content = content .. string.format("已解锁: %d 个\n", #(player_boons.unlocked or {}))
            content = content .. string.format("已装备: %d 个\n\n", #(player_boons.equipped or {}))
            content = content .. "开发中...\n"
            content = content .. "将包含移速、耐久、理智等被动效果"
        else
            content = content .. "无法获取被动信息"
        end
        
    elseif self.current_tab == "reputation" then
        content = "=== 阵营声望 ===\n\n"
        if player_rep and player_rep.reps then
            content = content .. string.format("猪人阵营: %d\n", player_rep.reps.pig or 0)
            content = content .. string.format("猫狸阵营: %d\n", player_rep.reps.cat or 0)
            content = content .. string.format("兔人阵营: %d\n\n", player_rep.reps.bunny or 0)
            content = content .. "声望效果:\n"
            content = content .. "- 交易折扣\n"
            content = content .. "- 护航协助\n"
            content = content .. "- 隐藏商品"
        else
            content = content .. "无法获取声望信息"
        end
        
    elseif self.current_tab == "caravan" then
        content = "=== 商队信息 ===\n\n"
        if world_comp and world_comp.caravan then
            local next_day = world_comp.caravan.next_day or 0
            local current_day = (TheWorld and TheWorld.state and TheWorld.state.cycles) or 0
            content = content .. string.format("下次商队: 第 %d 天\n", next_day)
            content = content .. string.format("当前天数: 第 %d 天\n\n", current_day)
            if next_day <= current_day then
                content = content .. "商队应该已经到达！\n"
            else
                content = content .. string.format("还需等待 %d 天\n", next_day - current_day)
            end
            content = content .. "\n商队功能:\n"
            content = content .. "- 回收冗余资源\n"
            content = content .. "- 兑换 Favor 代币\n"
            content = content .. "- 获取稀有蓝图"
        else
            content = content .. "无法获取商队信息"
        end
    end

    self.info_text:SetString(content)
end

function CaravanScreen:RefreshData()
    self:UpdateContent()
end

function CaravanScreen:Close()
    TheFrontEnd:PopScreen()
end

function CaravanScreen:OnControl(control, down)
    if CaravanScreen._base.OnControl(self, control, down) then
        return true
    end
    
    if not down and (control == CONTROL_CANCEL or control == CONTROL_MAP) then
        TheFrontEnd:GetSound():PlaySound("dontstarve/HUD/click_move")
        self:Close()
        return true
    end
    
    return false
end

function CaravanScreen:GetHelpText()
    local controller_id = TheInput:GetControllerID()
    local t = {}
    table.insert(t, TheInput:GetLocalizedControl(controller_id, CONTROL_CANCEL) .. " " .. STRINGS.UI.HELP.BACK)
    return table.concat(t, "  ")
end

return CaravanScreen
