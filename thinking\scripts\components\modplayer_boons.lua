-- Component files don't need GLOBAL declaration in DST

local ModPlayerBoons = Class(function(self, inst)
    self.inst = inst
    self.favor = 0
    self.unlocked = {}
    self.equipped = {}
end)

function ModPlayerBoons:CmdShowBoons()
    local msg = string.format("Favor:%d 已解锁:%d 已装备:%d", self.favor, #self.unlocked, #self.equipped)
    if self.inst.components.talker then
        self.inst.components.talker:Say(msg)
    end
end

function ModPlayerBoons:OnSave()
    return { favor = self.favor, unlocked = self.unlocked, equipped = self.equipped }
end

function ModPlayerBoons:OnLoad(data)
    if data then
        self.favor = data.favor or 0
        self.unlocked = data.unlocked or {}
        self.equipped = data.equipped or {}
    end
end

return ModPlayerBoons
