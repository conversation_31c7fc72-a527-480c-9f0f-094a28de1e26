-- Tuning file needs GLOBAL access

GLOBAL.TUNING.CARAVAN = {
    DIFFICULTY = GetModConfigData and GetModConfigData("difficulty") or "normal",
    MUTATOR_COUNT = GetModConfigData and GetModConfigData("mutator_count") or 2,
    CONTRACTS_CONCURRENT = GetModConfigData and GetModConfigData("contracts_concurrent") or 4,
    FAVOR_MULT = GetModConfigData and GetModConfigData("favor_multiplier") or 1.0,
    CARAVAN_PERIOD_DAYS = GetModConfigData and GetModConfigData("caravan_period") or 7,
    ENABLE_BOSS_EVO = GetModConfigData and GetModConfigData("enable_boss_evo") ~= false,
}

-- Difficulty presets (future use)
GLOBAL.TUNING.CARAVAN_DIFF = {
    easy = { favor_mult = 0.9 },
    normal = { favor_mult = 1.0 },
    hard = { favor_mult = 1.15 },
}

-- Strings (en/zh minimal)
GLOBAL.STRINGS.CARAVAN = {
    HELP = [[商旅巡游录可用指令(直接输入，无需斜杠):
chelp - 查看说明
cui - 打开/关闭UI界面 (推荐使用)
cmutators - 查看今日词条
creroll - 发起重掷词条投票（每人每日一次）
ccontracts - 查看当前合约
cboons - 查看/管理被动
crep - 查看阵营声望
caravan - 查看商队信息
creapply - 重新应用词条效果到所有玩家（调试用）

快捷键: 按 V 键打开/关闭UI界面]],
}

return GLOBAL.TUNING.CARAVAN
