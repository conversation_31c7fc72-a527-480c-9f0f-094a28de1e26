# 商旅巡游录 | Caravan Cycle: A+B 综合玩法蓝图（纯代码版）

> 目标：整合 组合A（每日词条+任务合约+被动恩惠）与 组合B（Boss进化+阵营声望+商队巡游），仅用代码与文本交互实现，无需新增贴图/音效/UI 资源。

## 一、核心循环
1. 每日黎明 → 随机生成 2 条全局词条（正向/负向各 1），玩家可 /reroll 发起投票每日限 1 次。
2. 世界存在一组可重复“远征合约”（任务） → 完成得 Favor（恩惠代币）与小量阵营声望。
3. 玩家使用 Favor 兑换/解锁“被动恩惠”（可装备上限 2）。
4. 阵营声望提升 → 获取交易折扣、护航协助、商队隐藏货品。
5. 定期出现商队 → 以给予/容器方式回收资源换 Favor/声望/蓝图。
6. Boss 根据全服行为进化（次数、词条强度、季节） → 战斗挑战与收益提升。

## 二、系统模块
### 2.1 每日词条（Daily Mutators）
- 触发：ListenForEvent("ms_nextcycle")（官方日更事件）。
- 规则：
  - 全服共享：同一世界当天所有玩家看到相同词条
  - 持久化：词条与 current_day 一起保存，读档后不变；仅在天数变化时重新生成
  - 每日随机 N 条（可配置、且不重复）从词条池选择
  - cmutators 查看今日词条；creroll 每人每日一次重掷（消耗个人次数）
- 词条类型与示例：
  - ✓ 正向：丰收之日（采集额外+1）、疾风步伐（移速大幅提升）、澄澈心智（理智不下降）
  - ✗ 负向：脆弱工具（耐久消耗翻倍）、不眠之夜（夜晚理智快速流失）、怪物磁铁（更易被发现）
  - ◈ 中性：玻璃大炮（攻击翻倍但生命减半）、夜猫子（夜晚加成白天虚弱）
  - ★ 事件：商人造访（特殊商队到访）
- 设计理念：符合饥荒整数数值系统，效果明显有冲击力，避免小数百分比。
- 实现：世界组件保存 mutators 与 current_day；玩家组件保存 last_reroll_day 与 reroll_used_today；UI 提供一键重掷。
- 效果系统：MutatorEffects 系统实现真实游戏机制，使用官方 API（locomotor、hunger、sanity、finiteuses 等）。

### 2.2 远征合约（Expedition Contracts）
- 存在形式：世界组件维护任务池（3–5 个同时存在）。
- 任务类型：
  - 击杀类：击杀触手 X 只、蜘蛛 X 只。
  - 交付类：给予猪王 X 物品、上交木板/石砖。
  - 建设类：修复/新建 X 个猪屋、路灯（用原生建造事件计数）。
  - 生态类：植树 X 棵、清理树精 X 次（击败树精）。
- 奖励：Favor n、阵营声望（与任务目标阵营相关）、少量随机蓝图几率。
- 命令：/contracts 查看、/contract <id> 接取（可选，默认共享进度）。
- 完成广播：TheNet:Announce，记录完成时间用于刷新。

### 2.3 被动恩惠（Passive Boons）
- 获取：消耗 Favor 解锁；装备上限 = 2（可配置）。
- 示例：
  - 轻装快行：+5% 移速（不叠加与坐骑）。
  - 匠心耐久：工具耐久消耗 -10%。
  - 夜行心定：夜晚理智损失 -20%。
  - 饥不择食：食物边际饱食更高（阈值调整）。
- 洗点：/boons respec（消耗 Favor），或有限次免费洗点（世界计次）。
- 数据：玩家组件保存“已解锁列表、已装备 slots、可用Favor”。

### 2.4 阵营声望（Factions & Reputation）
- 阵营 v1：猪（Pig）。v2：猫（Catcoon）或兔人（Bunnyman）。
- 获得/损失：
  - +：喂食、助阵击杀敌人、完成该阵营相关任务。
  - -：攻击阵营单位、偷取阵营容器、摧毁其建筑。
- 阈值奖励：
  - 低级：在其聚落附近获得护航（短时跟随帮助战斗）。
  - 中级：商队/交易折扣 5–10%。
  - 高级：解锁隐藏交易项或一次性强力恩惠（例如下一日可指定 1 条词条）。
- 命令：/rep 查看所有阵营当前分值与效果。

### 2.5 商队巡游（Caravans）
- 刷新：默认每 7 天 1 次，在生物群系边缘生成“商队点”（使用现有猪/猫单位作为商贩）。
- 交易：给予/容器交互，回收冗余资源（齿轮、宝石、种子、草枝）换 Favor/声望/蓝图。
- 公告：出现时全图公告+近距离 say() 提示；/caravan 查看大致位置（方向提示）。
- 进阶：v2 随机路线巡游与“停靠点”短暂停留（公告提示）。

### 2.6 Boss 进化（Boss Evolutions）
- 触发因子：
  - 季节（Season）、全服累计击杀次数、当日词条标签（如“寒潮”令巨鹿多一次踩地震）。
- v1 覆盖 Boss：巨鹿 Deerclops、海象群 MacTusk。
- 行为变体示例：
  - 巨鹿：周期性踩地震（低强度），可震落玩家手持工具；冰锥落点更分散。
  - 海象：夜间“侦察吼叫”引来 1 个增援猎犬或提高追踪距离一小段时间。
- 奖励：进化 Boss 掉落表增加小概率额外蓝图或 Favor 包。
- 实现：AddPrefabPostInit 注入事件/组件参数而非替换脑树；倍率可配置并可关。

## 三、命令与交互
### 3.1 UI界面（推荐）
- 快捷键：V 键打开/关闭（仅非聊天输入状态）
- 聊天命令：cui 打开/关闭
- 布局：
  - 顶部：标题“商旅巡游录”
  - 中上：内容文本区域（500x300）
  - 底部：标签页（词条/合约/恩惠/声望/商队），操作按钮区域位于其上（如“重掷词条”）
- 说明：
  - 已移除窗口底部的“刷新/关闭”栏，界面更简洁
  - 词条页提供“重掷词条”按钮，按钮状态与玩家当日次数联动
  - 不再通过聊天输入 v 开关UI，避免与聊天冲突

### 3.2 聊天命令（备用）
- chelp：显示本模组指令与简要说明
- cmutators, creroll：词条相关
- ccontracts：合约相关
- cboons：被动恩惠相关
- crep：阵营声望相关
- ccaravan：商队相关

## 四、数据结构与保存
- 世界组件 modworld_global（inst = TheWorld）
  - mutators: [{id, type, ...}] 当日激活的词条
  - current_day: number 记录词条对应的游戏天数
  - contracts: {id => {type, target, progress, goal, reward, expires}}
  - caravan: {next_day, last_spawn_pos}
  - boss_state: {deerclops = {evo, kills}, mactusk = {evo, kills}}
- 玩家组件 modplayer_boons
  - favor: number
  - unlocked_boons: set
  - equipped_boons: [slot1, slot2]
- 玩家组件 modplayer_rep
  - reps: {pig = number, cat = number, bunny = number}
- 玩家组件 modplayer_reroll（新增）
  - last_reroll_day: number 上次重掷所在天数
  - reroll_used_today: bool 今日是否已重掷

- 持久化：
  - 世界 OnSave/OnLoad：保存 mutators 与 current_day，读档后不被初始化覆盖
  - 玩家 OnSave/OnLoad：保存上面各自字段，进出存档后保持一致

## 五、配置项（modinfo.lua）
- 词条数量（1–3）与类别开关
- 合约并发数与刷新间隔
- Favor 奖励倍率、被动上限
- 商队周期与交易倍率
- Boss 进化开关与强度倍率

## 六、MVP 实施路线（进展更新）
1) 框架与组件 ✅
   - 搭建 modmain.lua、modinfo.lua、scripts/components/{modworld_global.lua, modplayer_boons.lua, modplayer_rep.lua, modplayer_reroll.lua}
   - 世界组件改为 AddPrefabPostInit("world") 挂载，保证 OnLoad 时序正确
   - 统一使用 ms_nextcycle 作为“新一天”触发
2) UI系统 ✅（第一版美化）
   - 创建 CaravanScreen 界面，标签页移到底部；移除底部“刷新/关闭”栏
   - V键快捷键 + cui命令打开UI（聊天状态不响应 V）
   - 词条页提供“重掷词条”按钮并与玩家每日次数联动
3) 每日词条 v1 ✅（持久化 + 每人每日一次重掷 + 真实效果）
   - 世界保存 mutators 与 current_day，读档后不变；新一天自动刷新
   - 玩家保存 last_reroll_day 与 reroll_used_today
   - 实现真实游戏效果：正向（丰收+1、疾风步伐、铁胃、澄澈心智、夜视）、负向（脆弱工具、不眠之夜、笨拙之手、负重前行）、中性（玻璃大炮）
4) 技术修复 ✅（GLOBAL变量访问问题）
   - 修复 DoTaskInTime 调用错误：组件中使用 self.inst:DoTaskInTime() 而非 self:DoTaskInTime()
   - 修复 GLOBAL 变量访问：在所有系统文件和组件文件中添加 local GLOBAL = rawget(_G, "GLOBAL") or _G
   - 涉及文件：mutator_effects.lua, commands.lua, modworld_global.lua
   - 解决 strict 模式下的变量声明错误，确保 AllPlayers 等全局变量正常访问
5) 合约 v1：2–3 种任务类型与事件计数（待实现）
6) Favor 与被动 v1：解锁/装备/2条被动效果（待实现）
7) 阵营声望 v1（猪）：声望获取与阈值奖励（待实现）
8) 商队 v1（猪商队）：固定交易表与刷新机制（待实现）
9) Boss 进化 v1：巨鹿、海象各1个行为变体（待实现）
10) 配置开关与数值整理，性能与兼容性检查

## 七、测试计划
### 7.1 基础功能测试
- 本地主机开图，Day 1 验证词条生成与 /reroll；Day 2 刷新新词条
- 刷新/完成合约计数正确，Favor 结算与被动效果生效
- 声望正负获取与阈值奖励触发；商队按周期出现并能交易
- 刷 Boss 观察变体是否触发，掉落是否附加
- 存档/读档数据保持一致

### 7.2 技术修复验证 ✅
- **GLOBAL变量访问测试**：使用 `dofile("mods/thinking/test_global_fix.lua")` 验证所有系统文件正常加载
- **组件方法调用测试**：确认世界组件中的DoTaskInTime正常工作，无运行时错误
- **词条效果应用测试**：验证MutatorEffects系统能正确访问AllPlayers等全局变量
- **UI界面测试**：确认V键和cui命令能正常打开界面，无GLOBAL相关错误
- **聊天命令测试**：验证commands.lua中的聊天命令系统正常工作

## 八、技术实现细节与修复记录
### 8.1 GLOBAL变量访问修复
**问题**：在饥荒DST的strict模式下，通过require加载的文件无法直接访问GLOBAL变量，导致运行时错误。

**解决方案**：在所有需要访问GLOBAL变量的文件开头添加：
```lua
-- 确保能访问全局变量
local GLOBAL = rawget(_G, "GLOBAL") or _G
```

**涉及文件**：
- `scripts/systems/mutator_effects.lua`
- `scripts/systems/commands.lua`
- `scripts/components/modworld_global.lua`

### 8.2 组件方法调用修复
**问题**：在组件中错误使用 `self:DoTaskInTime()`，但DoTaskInTime是实体方法，不是组件方法。

**解决方案**：使用 `self.inst:DoTaskInTime()` 来调用实体的定时任务方法。

**修复位置**：`modworld_global.lua` 第38行

### 8.3 环境兼容性
- 使用 `rawget(_G, "GLOBAL") or _G` 确保在不同mod环境中都能正常工作
- 避免直接访问未声明的全局变量，符合DST的strict模式要求
- 所有系统文件都采用统一的GLOBAL访问模式

## 九、兼容性与风险
- 钩子尽量使用 AddPrefabPostInit / AddComponentPostInit；少量行为微调通过组件参数
- 所有系统提供倍率/开关，默认中度
- 避免全局覆写；尽量事件驱动（ListenForEvent）
- 已解决GLOBAL变量访问和组件方法调用的兼容性问题

## 十、后续扩展
- 主题日、更多阵营与商队、任务宾果、更多 Boss 进化分支

---
本蓝图为实施依据，接下来将初始化代码骨架（无资源），按“六、MVP 实施路线”逐步实现与测试。

## 十一、修复日志
### 2024年修复记录
- **GLOBAL变量访问修复**：解决了systems目录和components目录下文件无法访问GLOBAL变量的问题
- **组件方法调用修复**：修正了组件中错误调用DoTaskInTime的问题
- **环境兼容性提升**：确保mod在不同DST环境中都能正常运行
- **测试框架完善**：添加了完整的技术修复验证测试

