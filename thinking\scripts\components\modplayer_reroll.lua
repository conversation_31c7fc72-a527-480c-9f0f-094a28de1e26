-- 玩家重投组件：跟踪每个玩家的重投次数
local ModPlayerReroll = Class(function(self, inst)
    self.inst = inst
    self.reroll_used_today = false
    self.last_reroll_day = -1

    -- 监听天数变化，重置重投次数（使用 ms_nextcycle）
    inst:ListenForEvent("ms_nextcycle", function() self:OnNewDay() end, TheWorld)
end)

function ModPlayerReroll:OnNewDay()
    local current_cycles = (TheWorld and TheWorld.state and TheWorld.state.cycles) or 0
    print("[商旅巡游录] Player", self.inst.userid or "unknown", "new day reset, day:", current_cycles)
    
    -- 新的一天，重置重投次数
    if self.last_reroll_day ~= current_cycles then
        self.last_reroll_day = current_cycles
        self.reroll_used_today = false
        print("[商旅巡游录] Player reroll reset for day", current_cycles)
    end
end

function ModPlayerReroll:CanReroll()
    local current_cycles = (TheWorld and TheWorld.state and TheWorld.state.cycles) or 0
    
    -- 检查是否是新的一天，如果是则重置
    if self.last_reroll_day ~= current_cycles then
        self:OnNewDay()
    end
    
    return not self.reroll_used_today
end

function ModPlayerReroll:UseReroll()
    local current_cycles = (TheWorld and TheWorld.state and TheWorld.state.cycles) or 0
    
    if self:CanReroll() then
        self.reroll_used_today = true
        self.last_reroll_day = current_cycles
        print("[商旅巡游录] Player", self.inst.userid or "unknown", "used reroll for day", current_cycles)
        return true
    end
    return false
end

function ModPlayerReroll:OnSave()
    return {
        reroll_used_today = self.reroll_used_today,
        last_reroll_day = self.last_reroll_day,
    }
end

function ModPlayerReroll:OnLoad(data)
    if data then
        self.reroll_used_today = data.reroll_used_today or false
        self.last_reroll_day = data.last_reroll_day or -1
        
        -- 加载后检查是否需要重置
        local current_cycles = (TheWorld and TheWorld.state and TheWorld.state.cycles) or 0
        if self.last_reroll_day ~= current_cycles then
            self:OnNewDay()
        end
    end
end

return ModPlayerReroll
